/* 材质网格组件样式 */
.materials-grid {
  display: grid;
  gap: var(--spacing-sm);
  overflow-y: auto;
  padding: var(--spacing-xs);
  margin: calc(-1 * var(--spacing-xs));
  align-content: flex-start;
  flex: 1;
  min-height: 0;
}

/* 默认变体 - 40px最小宽度的响应式网格 */
.materials-grid--default {
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
}

/* 预设材质变体 - 固定60px宽度的网格 */
.materials-grid--preset {
  grid-template-columns: repeat(auto-fill, 60px);
  border-radius: var(--radius-lg);
}

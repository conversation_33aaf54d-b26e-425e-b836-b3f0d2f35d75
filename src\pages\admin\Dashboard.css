.dashboard-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: var(--color-bg-page);
  overflow-x: hidden;
}



.dashboard-sidebar {
  display: flex;
  flex-direction: column;
  width: var(--sidebar-width);
  background-color: var(--color-bg-primary);
  border-right: var(--border-width) solid var(--color-border);
  padding: var(--spacing-lg) 0;
}

.sidebar-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-xxxl);
}

.logo-container {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s, background-color 0.2s;
  padding: var(--spacing-sm);
  border-radius: var(--radius-base);
}

.logo-tooltip {
  position: absolute;
  bottom: calc(-1 * var(--spacing-xl) - var(--spacing-xs));
  background-color: var(--color-support);
  color: var(--color-content-accent);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  opacity: 0;
  visibility: hidden;
  transform: translateY(calc(-1 * var(--spacing-xs)));
  transition: var(--transition-base);
  white-space: nowrap;
  z-index: var(--z-index-overlay);
}

.sidebar-header .logo {
  height: var(--button-height-large);
  margin-bottom: var(--spacing-base);
}

.sidebar-header h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
  margin: 0;
}

.sidebar-menu {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  list-style: none;
}

/* 自定义菜单项 */
.menu-item {
  width: 100%;
  height: var(--button-height-large);
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
  padding: 0 var(--spacing-xl);
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--color-content-regular);
}

.menu-item:hover {
  background-color: var(--color-bg-hover);
}

.menu-item.active {
  background-color: var(--color-support);
  color: var(--color-content-accent);
}


.sidebar-footer {
  padding: 0 var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.logout-button {
  display: flex;
  align-items: center;
  height: var(--button-height-large);
  gap: var(--spacing-base);
  padding: var(--spacing-base) var(--spacing-md);
  border-radius: var(--radius-base);
  cursor: pointer;
  color: var(--color-content-regular);
  transition: all 0.2s ease;
}

.logout-button:hover {
  background-color: var(--color-support-hover);
  color: var(--color-content-accent);
}

.dashboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xxxl);
  border-bottom: var(--border-width) solid var(--color-border);
}

.content-header h1 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
  margin: 0;
}

.content-body {
  flex: 1;
  padding: var(--spacing-xl) var(--spacing-xxxl);
  overflow-y: auto;
}

/* 总览页面样式 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(var(--grid-min-width-large), 1fr));
  gap: var(--card-gap-large);
  margin-bottom: var(--card-margin-bottom);
}

.stat-card {
  background-color: var(--color-bg-overlay);
  border-radius: var(--radius-base);
  padding: var(--card-padding);
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: var(--card-gap);
  transition: background-color var(--transition-base), transform var(--transition-base);
  cursor: pointer;
}

.stat-card:hover {
  background-color: var(--color-bg-hover);
  transform: translateY(var(--transform-hover));
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--icon-container-size);
  height: var(--icon-container-size);
  background-color: var(--color-brand);
  border-radius: var(--radius-base);
  color: var(--color-content-invert);
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-content-regular);
}

.quick-actions {
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-base);
  padding: var(--card-padding);
}

.quick-actions h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
  margin-top: 0;
  margin-bottom: var(--card-margin-small);
}

.quick-action-links {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(var(--grid-min-width-medium), 1fr));
  gap: var(--card-gap);
}

.quick-action-item {
  display: flex;
  align-items: center;
  gap: var(--card-gap-small);
  padding: var(--transform-small);
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-base);
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-content-regular);
}

.quick-action-item:hover {
  background-color: var(--color-bg-hover);
  color: var(--color-content-accent);
}
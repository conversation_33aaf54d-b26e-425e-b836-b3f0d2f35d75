import React from 'react';
import type { LucideIcon } from 'lucide-react';
import { Info, CheckCircle, AlertTriangle, XCircle } from 'lucide-react';
import './status-message.css';

export interface StatusMessageProps {
  /** 状态类型 */
  type?: 'info' | 'success' | 'warning' | 'error';
  /** 自定义图标 */
  icon?: LucideIcon;
  /** 主要消息 */
  message: string;
  /** 次要描述 */
  description?: string;
  /** 自定义类名 */
  className?: string;
  /** 尺寸 */
  size?: 'small' | 'medium' | 'large';
  /** 是否显示图标 */
  showIcon?: boolean;
}

const DEFAULT_ICONS = {
  info: Info,
  success: CheckCircle,
  warning: AlertTriangle,
  error: XCircle
};

export const StatusMessage: React.FC<StatusMessageProps> = ({
  type = 'info',
  icon,
  message,
  description,
  className = '',
  size = 'medium',
  showIcon = true
}) => {
  const Icon = icon || DEFAULT_ICONS[type];
  
  const containerClasses = [
    'status-message',
    `status-message--${type}`,
    `status-message--${size}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {showIcon && <Icon className="status-message__icon" />}
      <div className="status-message__content">
        <div className="status-message__message">{message}</div>
        {description && (
          <div className="status-message__description">{description}</div>
        )}
      </div>
    </div>
  );
};

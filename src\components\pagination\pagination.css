/* 分页器组件样式 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--color-border);
  margin-top: var(--spacing-md);
}

.pagination__total {
  color: var(--color-content-secondary);
  font-size: var(--font-size-sm);
  white-space: nowrap;
}

.pagination__controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.pagination__pages {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.pagination__page {
  min-width: var(--spacing-xl);
  height: var(--spacing-xl);
  padding: 0;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-bg-base);
  color: var(--color-content-primary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination__page:hover:not(:disabled) {
  border-color: var(--color-brand);
  color: var(--color-brand);
}

.pagination__page--active {
  background: var(--color-brand);
  border-color: var(--color-brand);
  color: var(--color-content-inverse);
}

.pagination__page--active:hover {
  background: var(--color-brand-hover);
  border-color: var(--color-brand-hover);
}

.pagination__page:disabled {
  background: var(--color-bg-disabled);
  border-color: var(--color-border-disabled);
  color: var(--color-content-disabled);
  cursor: not-allowed;
}

.pagination__ellipsis {
  padding: 0 var(--spacing-xs);
  color: var(--color-content-secondary);
  font-size: var(--font-size-sm);
  user-select: none;
}

.pagination__size-changer {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  white-space: nowrap;
}

.pagination__size-label {
  color: var(--color-content-secondary);
  font-size: var(--font-size-sm);
}

.pagination__size-select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-bg-base);
  color: var(--color-content-primary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: border-color var(--transition-base);
}

.pagination__size-select:hover {
  border-color: var(--color-brand);
}

.pagination__size-select:focus {
  outline: none;
  border-color: var(--color-brand);
  box-shadow: 0 0 0 2px var(--color-brand-alpha);
}

.pagination__size-select:disabled {
  background: var(--color-bg-disabled);
  border-color: var(--color-border-disabled);
  color: var(--color-content-disabled);
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: var(--breakpoint-small)) {
  .pagination {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
  
  .pagination__controls {
    justify-content: center;
  }
  
  .pagination__total,
  .pagination__size-changer {
    text-align: center;
  }
  
  .pagination__pages {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: var(--breakpoint-tiny)) {
  .pagination__page {
    min-width: var(--spacing-lg);
    height: var(--spacing-lg);
    font-size: var(--font-size-xs);
  }
  
  .pagination__size-changer {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}
import { useRef, useEffect, useCallback } from 'react';
import * as THREE from 'three';
import { ThreePerformanceMonitor, MaterialOptimizer, GeometryOptimizer } from '../utils/threeUtils';

export interface ThreePerformanceOptions {
  /** 是否启用性能监控 */
  enableMonitoring?: boolean;
  /** 是否自动优化材质 */
  autoOptimizeMaterials?: boolean;
  /** 是否自动优化几何体 */
  autoOptimizeGeometries?: boolean;
  /** 性能警告阈值（毫秒） */
  performanceThreshold?: number;
}

/**
 * Three.js 性能优化Hook
 */
export const useThreePerformance = (
  renderer: THREE.WebGLRenderer | null,
  options: ThreePerformanceOptions = {}
) => {
  const {
    enableMonitoring = true,
    autoOptimizeMaterials = true,
    autoOptimizeGeometries = true,
    performanceThreshold = 16.67 // 60fps
  } = options;

  const monitorRef = useRef<ThreePerformanceMonitor | null>(null);
  const frameCountRef = useRef(0);
  const performanceDataRef = useRef<{
    avgFrameTime: number;
    maxFrameTime: number;
    minFrameTime: number;
    frameCount: number;
  }>({
    avgFrameTime: 0,
    maxFrameTime: 0,
    minFrameTime: Infinity,
    frameCount: 0
  });

  // 初始化性能监控器
  useEffect(() => {
    if (renderer && enableMonitoring) {
      monitorRef.current = new ThreePerformanceMonitor(renderer);
    }
    
    return () => {
      monitorRef.current = null;
    };
  }, [renderer, enableMonitoring]);

  // 开始帧监控
  const startFrame = useCallback(() => {
    if (monitorRef.current) {
      monitorRef.current.startFrame();
    }
  }, []);

  // 结束帧监控
  const endFrame = useCallback(() => {
    if (monitorRef.current) {
      monitorRef.current.endFrame();
      
      const stats = monitorRef.current.getStats();
      frameCountRef.current++;
      
      // 更新性能统计
      const data = performanceDataRef.current;
      data.frameCount = frameCountRef.current;
      data.avgFrameTime = (data.avgFrameTime * (data.frameCount - 1) + stats.frameTime) / data.frameCount;
      data.maxFrameTime = Math.max(data.maxFrameTime, stats.frameTime);
      data.minFrameTime = Math.min(data.minFrameTime, stats.frameTime);
      
      // 性能警告
      if (stats.frameTime > performanceThreshold) {
        console.warn(`Frame time exceeded threshold: ${stats.frameTime.toFixed(2)}ms`);
      }
    }
  }, [performanceThreshold]);

  // 优化场景
  const optimizeScene = useCallback((scene: THREE.Object3D) => {
    if (!scene) return;
    
    const startTime = performance.now();
    
    if (autoOptimizeMaterials) {
      MaterialOptimizer.optimizeSceneMaterials(scene);
    }
    
    if (autoOptimizeGeometries) {
      GeometryOptimizer.optimizeSceneGeometries(scene);
    }
    
    const optimizationTime = performance.now() - startTime;
    console.log(`Scene optimization completed in ${optimizationTime.toFixed(2)}ms`);
  }, [autoOptimizeMaterials, autoOptimizeGeometries]);

  // 获取当前性能统计
  const getPerformanceStats = useCallback(() => {
    const currentStats = monitorRef.current?.getStats();
    const historicalData = performanceDataRef.current;
    
    return {
      current: currentStats || null,
      historical: {
        ...historicalData,
        avgFrameTime: Number(historicalData.avgFrameTime.toFixed(2)),
        maxFrameTime: Number(historicalData.maxFrameTime.toFixed(2)),
        minFrameTime: historicalData.minFrameTime === Infinity ? 0 : Number(historicalData.minFrameTime.toFixed(2))
      }
    };
  }, []);

  // 重置性能统计
  const resetStats = useCallback(() => {
    if (monitorRef.current) {
      monitorRef.current.reset();
    }
    
    frameCountRef.current = 0;
    performanceDataRef.current = {
      avgFrameTime: 0,
      maxFrameTime: 0,
      minFrameTime: Infinity,
      frameCount: 0
    };
  }, []);

  // 检查WebGL能力
  const checkWebGLCapabilities = useCallback(() => {
    if (!renderer) return null;
    
    const gl = renderer.getContext();
    const capabilities = renderer.capabilities;
    
    return {
      maxTextureSize: capabilities.maxTextureSize,
      maxCubemapSize: capabilities.maxCubemapSize,
      maxVertexTextures: capabilities.maxVertexTextures,
      maxFragmentUniforms: capabilities.maxFragmentUniforms,
      maxVertexUniforms: capabilities.maxVertexUniforms,
      maxVaryings: capabilities.maxVaryings,
      maxVertexAttribs: capabilities.maxVertexAttribs,
      floatFragmentTextures: capabilities.floatFragmentTextures,
      floatVertexTextures: capabilities.floatVertexTextures,
      maxAnisotropy: capabilities.getMaxAnisotropy(),
      extensions: {
        derivatives: gl.getExtension('OES_standard_derivatives') !== null,
        fragDepth: gl.getExtension('EXT_frag_depth') !== null,
        drawBuffers: gl.getExtension('WEBGL_draw_buffers') !== null,
        shaderTextureLOD: gl.getExtension('EXT_shader_texture_lod') !== null
      }
    };
  }, [renderer]);

  // 内存使用情况
  const getMemoryUsage = useCallback(() => {
    if (!renderer) return null;
    
    const info = renderer.info;
    return {
      geometries: info.memory.geometries,
      textures: info.memory.textures,
      programs: info.programs?.length || 0
    };
  }, [renderer]);

  return {
    startFrame,
    endFrame,
    optimizeScene,
    getPerformanceStats,
    resetStats,
    checkWebGLCapabilities,
    getMemoryUsage
  };
};

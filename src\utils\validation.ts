/**
 * 通用验证工具函数
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface ValidationRule<T> {
  validate: (value: T) => boolean;
  message: string;
}

/**
 * 创建验证器
 */
export const createValidator = <T>(rules: ValidationRule<T>[]) => {
  return (value: T): ValidationResult => {
    const errors: string[] = [];
    
    for (const rule of rules) {
      if (!rule.validate(value)) {
        errors.push(rule.message);
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  };
};

/**
 * 常用验证规则
 */
export const ValidationRules = {
  required: <T>(message: string = '此字段为必填项'): ValidationRule<T> => ({
    validate: (value: T) => {
      if (value === null || value === undefined) return false;
      if (typeof value === 'string') return value.trim().length > 0;
      if (Array.isArray(value)) return value.length > 0;
      return true;
    },
    message
  }),

  minLength: (min: number, message?: string): ValidationRule<string> => ({
    validate: (value: string) => !value || value.length >= min,
    message: message || `最少需要${min}个字符`
  }),

  maxLength: (max: number, message?: string): ValidationRule<string> => ({
    validate: (value: string) => !value || value.length <= max,
    message: message || `最多允许${max}个字符`
  }),

  email: (message: string = '请输入有效的邮箱地址'): ValidationRule<string> => ({
    validate: (value: string) => {
      if (!value) return true;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value);
    },
    message
  }),

  url: (message: string = '请输入有效的URL地址'): ValidationRule<string> => ({
    validate: (value: string) => {
      if (!value) return true;
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    },
    message
  }),

  numeric: (message: string = '请输入有效的数字'): ValidationRule<string | number> => ({
    validate: (value: string | number) => {
      if (value === '' || value === null || value === undefined) return true;
      return !isNaN(Number(value));
    },
    message
  }),

  range: (min: number, max: number, message?: string): ValidationRule<number> => ({
    validate: (value: number) => value >= min && value <= max,
    message: message || `值必须在${min}到${max}之间`
  }),

  fileSize: (maxSizeBytes: number, message?: string): ValidationRule<File> => ({
    validate: (file: File) => file.size <= maxSizeBytes,
    message: message || `文件大小不能超过${Math.round(maxSizeBytes / (1024 * 1024))}MB`
  }),

  fileType: (allowedTypes: string[], message?: string): ValidationRule<File> => ({
    validate: (file: File) => {
      const extension = file.name.split('.').pop()?.toLowerCase() || '';
      return allowedTypes.includes(extension);
    },
    message: message || `只允许${allowedTypes.join(', ')}格式的文件`
  }),

  custom: <T>(
    validateFn: (value: T) => boolean,
    message: string
  ): ValidationRule<T> => ({
    validate: validateFn,
    message
  })
};

/**
 * 表单验证器
 */
export class FormValidator {
  private rules: Record<string, ValidationRule<unknown>[]> = {};

  addRule<T>(fieldName: string, rule: ValidationRule<T>) {
    if (!this.rules[fieldName]) {
      this.rules[fieldName] = [];
    }
    this.rules[fieldName].push(rule);
    return this;
  }

  addRules<T>(fieldName: string, rules: ValidationRule<T>[]) {
    if (!this.rules[fieldName]) {
      this.rules[fieldName] = [];
    }
    this.rules[fieldName].push(...rules);
    return this;
  }

  validate(data: Record<string, unknown>): Record<string, ValidationResult> {
    const results: Record<string, ValidationResult> = {};
    
    for (const [fieldName, rules] of Object.entries(this.rules)) {
      const value = data[fieldName];
      const validator = createValidator(rules);
      results[fieldName] = validator(value);
    }
    
    return results;
  }

  isValid(data: Record<string, unknown>): boolean {
    const results = this.validate(data);
    return Object.values(results).every(result => result.isValid);
  }

  getErrors(data: Record<string, unknown>): Record<string, string[]> {
    const results = this.validate(data);
    const errors: Record<string, string[]> = {};
    
    for (const [fieldName, result] of Object.entries(results)) {
      if (!result.isValid) {
        errors[fieldName] = result.errors;
      }
    }
    
    return errors;
  }
}

/**
 * 预定义的验证器
 */
export const Validators = {
  modelName: createValidator([
    ValidationRules.required('模型名称不能为空'),
    ValidationRules.minLength(2, '模型名称至少需要2个字符'),
    ValidationRules.maxLength(50, '模型名称不能超过50个字符')
  ]),

  modelFile: createValidator([
    ValidationRules.required('请选择模型文件'),
    ValidationRules.fileType(['glb', 'gltf'], '只支持GLB和GLTF格式的模型文件'),
    ValidationRules.fileSize(50 * 1024 * 1024, '模型文件大小不能超过50MB')
  ]),

  materialProperty: createValidator([
    ValidationRules.numeric('请输入有效的数值'),
    ValidationRules.range(0, 1, '值必须在0到1之间')
  ])
};

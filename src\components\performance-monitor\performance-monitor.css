/* 性能监控组件样式 */
.performance-monitor {
  position: fixed;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: var(--color-bg-overlay);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  min-width: 200px;
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
  z-index: 1000;
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-lg);
}

.performance-monitor__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--color-border);
}

.performance-monitor__header h3 {
  margin: 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.performance-monitor__close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: var(--font-size-lg);
  line-height: 1;
  padding: 0;
  width: var(--icon-size-large);
  height: var(--icon-size-large);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.performance-monitor__close:hover {
  background: var(--color-bg-hover);
  color: var(--color-text-primary);
}

.performance-monitor__stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-normal);
}

.stat-value {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  font-family: var(--font-mono);
}

.performance-monitor__tips {
  margin-top: var(--spacing-xs);
  padding-top: var(--spacing-xs);
  border-top: 1px solid var(--color-border);
  text-align: center;
  color: var(--color-text-tertiary);
}

/* 性能等级样式 */
.performance-monitor--excellent {
  border-color: var(--color-success);
}

.performance-monitor--excellent .stat-value {
  color: var(--color-success);
}

.performance-monitor--good {
  border-color: var(--color-primary);
}

.performance-monitor--good .stat-value {
  color: var(--color-primary);
}

.performance-monitor--fair {
  border-color: var(--color-warning);
}

.performance-monitor--fair .stat-value {
  color: var(--color-warning);
}

.performance-monitor--poor {
  border-color: var(--color-error);
}

.performance-monitor--poor .stat-value {
  color: var(--color-error);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .performance-monitor {
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    min-width: var(--input-width-narrow);
    font-size: var(--font-size-sm);
  }
}

/* 动画效果 */
.performance-monitor {
  animation: slideIn var(--animation-duration) ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

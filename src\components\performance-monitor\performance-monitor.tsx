import React, { useState, useEffect, useRef } from 'react';
import type { WebGLRenderer } from 'three';
import { 
  startPerformanceMonitoring, 
  stopPerformanceMonitoring,
  autoOptimizeRenderer,
  isLowPerformanceDevice
} from '../../utils/performanceOptimizer';
import './performance-monitor.css';

interface PerformanceMonitorProps {
  renderer?: WebGLRenderer | null;
  showDetails?: boolean;
  enableAutoOptimization?: boolean;
}

/**
 * 性能监控组件
 * 显示FPS和内存使用情况，并可以自动优化渲染设置
 */
const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  renderer,
  showDetails = false,
  enableAutoOptimization = false
}) => {
  const [fps, setFps] = useState<number>(0);
  const [memory, setMemory] = useState<number | undefined>(undefined);
  const [expanded, setExpanded] = useState<boolean>(false);
  const [isLowPerformance, setIsLowPerformance] = useState<boolean>(false);
  const optimizationInterval = useRef<number | null>(null);

  // 启动性能监控
  useEffect(() => {
    // 检测是否为低性能设备
    setIsLowPerformance(isLowPerformanceDevice());
    
    // 使用防抖更新状态，避免频繁触发渲染
    let lastUpdateTime = 0;
    const updateInterval = 1000; // 每秒最多更新一次UI
    
    // 启动性能监控
    startPerformanceMonitoring((data) => {
      const now = performance.now();
      if (now - lastUpdateTime > updateInterval) {
        setFps(data.fps);
        if (data.memory !== undefined) {
          setMemory(data.memory);
        }
        lastUpdateTime = now;
      }
    }, 1000);
    
    return () => {
      stopPerformanceMonitoring();
      if (optimizationInterval.current) {
        clearInterval(optimizationInterval.current);
      }
    };
  }, []);
  
  // 自动优化渲染器设置
  useEffect(() => {
    if (!enableAutoOptimization || !renderer) return;
    
    // 每5秒检查一次性能并优化
    optimizationInterval.current = window.setInterval(() => {
      autoOptimizeRenderer(renderer);
    }, 5000);
    
    return () => {
      if (optimizationInterval.current) {
        clearInterval(optimizationInterval.current);
        optimizationInterval.current = null;
      }
    };
  }, [enableAutoOptimization, renderer]);

  // 根据FPS确定性能状态
  const getPerformanceStatus = (): 'good' | 'warning' | 'critical' => {
    if (fps >= 45) return 'good';
    if (fps >= 25) return 'warning';
    return 'critical';
  };
  
  const performanceStatus = getPerformanceStatus();

  return (
    <div className={`performance-monitor ${expanded ? 'expanded' : ''}`}>
      <div 
        className={`performance-indicator ${performanceStatus}`}
        onClick={() => setExpanded(!expanded)}
      >
        <span className="fps-value">{fps}</span>
        <span className="fps-label">FPS</span>
      </div>
      
      {expanded && showDetails && (
        <div className="performance-details">
          <div className="detail-item">
            <span className="detail-label">FPS:</span>
            <span className="detail-value">{fps}</span>
          </div>
          
          {memory !== undefined && (
            <div className="detail-item">
              <span className="detail-label">内存:</span>
              <span className="detail-value">{memory}%</span>
            </div>
          )}
          
          {isLowPerformance && (
            <div className="performance-warning">
              检测到低性能设备，已自动优化渲染设置
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PerformanceMonitor;

import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload } from 'lucide-react';
import { FILE_VALIDATION_CONFIGS, createDragUploadHandlers, handleFileInputChange } from '../../utils/fileUpload';
import './upload-model-card.css';

interface UploadModelCardProps {
  className?: string;
  onFileSelect?: (file: File) => void;
  onError?: (error: string) => void;
}

export const UploadModelCard: React.FC<UploadModelCardProps> = ({ 
  className = '',
  onFileSelect,
  onError
}) => {
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);

  const handleFileSelectInternal = (file: File) => {
    // 如果有自定义的文件选择处理函数，使用它；否则导航到渲染页面
    if (onFileSelect) {
      onFileSelect(file);
    } else {
      navigate('/render', {
        state: { uploadedFile: file }
      });
    }
  };

  const handleErrorInternal = (error: string) => {
    onError?.(error);
    alert(error);
  };

  const dragHandlers = createDragUploadHandlers(
    handleFileSelectInternal,
    setDragOver,
    FILE_VALIDATION_CONFIGS.MODEL,
    handleErrorInternal
  );

  const handleClick = () => {
    inputRef.current?.click();
  };

  return (
    <div className={`model-card upload-model-card ${className}`}>
      <h3 className="model-card__title">上传模型</h3>
      <div 
        className={`upload-model-card__content ${dragOver ? 'drag-over' : ''}`}
        onDrop={dragHandlers.onDrop}
        onDragOver={dragHandlers.onDragOver}
        onDragLeave={dragHandlers.onDragLeave}
        onClick={handleClick}
      >
        <div className="upload-model-card__icon">
          <Upload size={32} />
        </div>
        <div className="upload-model-card__text">
          <p className="upload-model-card__primary">点击或拖拽模型文件到此处上传</p>
          <p className="upload-model-card__secondary">支持格式：GLB、GLTF</p>
        </div>
        <input
          ref={inputRef}
          type="file"
          accept=".glb,.gltf"
          className="upload-model-card__input"
          onChange={(e) => {
            handleFileInputChange(
              e,
              handleFileSelectInternal,
              FILE_VALIDATION_CONFIGS.MODEL,
              handleErrorInternal
            );
          }}
        />
      </div>
    </div>
  );
};

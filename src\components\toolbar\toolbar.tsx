import React from 'react';
import './toolbar.css';

export interface ToolbarProps {
  /** 左侧内容（通常是搜索框） */
  left?: React.ReactNode;
  /** 右侧内容（通常是操作按钮） */
  right?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 是否显示分隔线 */
  showDivider?: boolean;
}

export const Toolbar: React.FC<ToolbarProps> = ({
  left,
  right,
  className = '',
  showDivider = false
}) => {
  const containerClasses = [
    'toolbar',
    showDivider ? 'toolbar--with-divider' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {left && (
        <div className="toolbar__left">
          {left}
        </div>
      )}
      {right && (
        <div className="toolbar__right">
          {right}
        </div>
      )}
    </div>
  );
};

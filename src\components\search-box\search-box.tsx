import React from 'react';
import { Search } from 'lucide-react';
import './search-box.css';

export interface SearchBoxProps {
  value?: string;
  placeholder?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  disabled?: boolean;
  className?: string;
  width?: string;
}

export const SearchBox: React.FC<SearchBoxProps> = ({
  value = '',
  placeholder = '搜索',
  onChange,
  onSearch,
  disabled = false,
  className = '',
  width
}) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange?.(newValue);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearch?.(value);
    }
  };

  const handleSearchClick = () => {
    onSearch?.(value);
  };

  const searchBoxClasses = [
    'search-box',
    disabled && 'search-box--disabled',
    className
  ].filter(Boolean).join(' ');

  const style = width ? { width } : undefined;

  return (
    <div className={searchBoxClasses} style={style}>
      <div className="search-box__icon-container" onClick={handleSearchClick}>
        <Search className="search-box__icon" />
      </div>
      <input
        className="search-box__input"
        type="text"
        value={value}
        placeholder={placeholder}
        onChange={handleInputChange}
        onKeyPress={handleKeyPress}
        disabled={disabled}
      />
    </div>
  );
}
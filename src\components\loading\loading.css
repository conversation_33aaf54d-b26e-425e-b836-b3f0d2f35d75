/* 加载组件基础样式 */
.loading {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  white-space: nowrap;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 默认变体 */
.loading--default {
  background: var(--loading-container-bg);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
}

/* 最小化变体 */
.loading--minimal {
  background: transparent;
  padding: var(--spacing-sm);
}

/* 覆盖层变体 */
.loading--overlay {
  background: var(--color-bg-overlay);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(4px);
}

/* 尺寸变体 */
.loading--small {
  font-size: var(--font-size-xs);
}

.loading--small .loading__spinner {
  width: var(--icon-size-small);
  height: var(--icon-size-small);
}

.loading--medium {
  font-size: var(--font-size-sm);
}

.loading--medium .loading__spinner {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
}

.loading--large {
  font-size: var(--font-size-base);
}

.loading--large .loading__spinner {
  width: var(--icon-size-large);
  height: var(--icon-size-large);
}

.loading--centered {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: auto;
  height: auto;
  min-height: 0;
}

/* 加载动画 */
.loading__spinner {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
  border-width: var(--outline-width);
  border-radius: 50%;
  border-style: solid;
  border-color: var(--color-content-mute);
  border-top-color: var(--color-content-accent);
  animation: loading-spin var(--animation-duration) linear infinite;
  flex-shrink: 0;
}

.loading__text {
  color: var(--color-content-accent);
  font-size: var(--font-size-base);
  line-height: 1;
  margin: 0;
}

/* 旋转动画 */
@keyframes loading-spin {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}

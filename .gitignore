# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Database
backend/database.sqlite

# Uploads
# backend/uploads/

# Environment variables
.env
.env.local
.env.production
.env.production
!.env.example

# Deployment
backups/
*.pem
*.key
*.crt
*.csr

# Docker
postgres_dev_data/

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

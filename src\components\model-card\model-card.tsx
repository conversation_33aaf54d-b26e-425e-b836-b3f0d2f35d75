import React from 'react';
import { useNavigate } from 'react-router-dom';
import type { ModelData } from '../../services/api';
import './model-card.css';

interface ModelCardProps {
  model: ModelData;
  className?: string;
}

export const ModelCard: React.FC<ModelCardProps> = ({ model, className = '' }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/render/${model.id}`);
  };

  return (
    <div className={`model-card ${className}`} onClick={handleClick}>
      <h3 className="model-card__title">{model.name}</h3>
      <div className="model-card__image-container">
        {model.thumbnailPath ? (
          <img
            src={model.thumbnailPath}
            alt={model.name}
            className="model-card__image"
            onError={(e) => {
              // 如果图片加载失败，显示一个占位图或默认图片
              const target = e.target as HTMLImageElement;
              target.src = '/placeholder-model.png';
              target.onerror = null; // 防止无限循环
            }}
          />
        ) : (
          <div className="model-card__placeholder">
            <span>暂无缩略图</span>
          </div>
        )}
      </div>
    </div>
  );
};
.custom-material-panel {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-base);
  width: 100%;
}

.color-picker-container {
  width: 100%;
  max-width: 100%;
  border-radius: var(--radius-base);
  overflow: hidden;
}

/* 自定义react-colorful组件的样式 */
.custom-material-panel .react-colorful {
  width: 100% !important;
  height: 140px !important;
  border-radius: var(--radius-base);
  overflow: visible;
  border: 1px solid var(--color-border);
  margin-bottom: 0;
  box-shadow: var(--shadow-sm);
}

/* 双色板样式 */
.custom-material-panel .react-colorful__saturation {
  border-radius: var(--radius-base) var(--radius-base) 0 0;
  border-bottom: none;
}

/* 色相条样式 */
.custom-material-panel .react-colorful__hue {
  height: 28px;
  border-radius: 0 0 var(--radius-base) var(--radius-base);
}

/* 调整选择器指示点样式 */
.custom-material-panel .react-colorful__pointer {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.8);
  border: 2px solid var(--color-content-invert);
  transform: translate(-50%, -50%);
}

/* 纹理上传区域 */
.custom-material-panel .texture-upload-area {
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--spacing-md);
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-content-secondary);
  background-color: var(--color-bg-hover);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  justify-content: center;
  min-height: 80px;
  position: relative;
}

.custom-material-panel .texture-upload-area.drag-over,
.custom-material-panel .texture-upload-area:hover {
  background-color: var(--color-brand-bg);
  border-color: var(--color-brand);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.custom-material-panel .texture-upload-area span {
  font-size: var(--font-size-sm);
  color: var(--color-content-regular);
  font-weight: var(--font-weight-medium);
  margin: 0;
}

.custom-material-panel .texture-upload-area small {
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  margin: 0;
}

/* 纹理预览样式 */
.custom-material-panel .texture-preview {
  position: relative;
  border-radius: var(--radius-base);
  overflow: hidden;
  border: 1px solid var(--color-border);
  background-color: var(--color-bg-overlay);
  box-shadow: var(--shadow-sm);
}

.custom-material-panel .texture-image {
  width: 100%;
  height: 100px;
  object-fit: cover;
  display: block;
  transition: transform 0.2s ease;
}

.custom-material-panel .texture-preview:hover .texture-image {
  transform: scale(1.02);
}

.custom-material-panel .remove-texture-btn {
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-content-invert);
  backdrop-filter: blur(4px);
}

.custom-material-panel .remove-texture-btn:hover {
  background-color: var(--color-danger);
  border-color: var(--color-danger);
  color: var(--color-content-invert);
  transform: scale(1.1);
}


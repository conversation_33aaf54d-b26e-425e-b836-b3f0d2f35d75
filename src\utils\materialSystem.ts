import * as THREE from 'three';
import { ProceduralMaterialFactory } from './shaderUtils';

/**
 * 材质系统规则管理器
 * 确保只有以'Editable'开头的材质可以在渲染界面调节
 */

export interface MaterialInfo {
  name: string;
  meshName: string;
  isEditable: boolean;
  originalMaterial: THREE.Material;
  currentMaterial: THREE.Material;
  properties: {
    color: string;
    metalness: number;
    roughness: number;
    opacity: number;
    textureUrl?: string;
  };
}

export interface MaterialPreset {
  id: string;
  name: string;
  category: 'metal' | 'plastic' | 'wood' | 'stone' | 'fabric' | 'glass' | 'procedural';
  properties: {
    color: string;
    metalness: number;
    roughness: number;
    opacity: number;
    textureUrl?: string;
  };
  proceduralType?: 'noise' | 'voronoi' | 'wood' | 'marble';
  proceduralOptions?: Record<string, unknown>;
}

/**
 * 材质系统管理器
 */
export class MaterialSystemManager {
  private materials = new Map<string, MaterialInfo>();
  private presets: MaterialPreset[] = [];
  private proceduralMaterials = new Map<string, THREE.ShaderMaterial>();

  constructor() {
    this.initializePresets();
  }

  /**
   * 初始化预设材质
   */
  private initializePresets() {
    this.presets = [
      // 金属材质
      {
        id: 'metal-steel',
        name: '钢铁',
        category: 'metal',
        properties: { color: '#C0C0C0', metalness: 1.0, roughness: 0.2, opacity: 1.0 }
      },
      {
        id: 'metal-gold',
        name: '黄金',
        category: 'metal',
        properties: { color: '#FFD700', metalness: 1.0, roughness: 0.1, opacity: 1.0 }
      },
      {
        id: 'metal-copper',
        name: '铜',
        category: 'metal',
        properties: { color: '#B87333', metalness: 1.0, roughness: 0.3, opacity: 1.0 }
      },
      
      // 塑料材质
      {
        id: 'plastic-white',
        name: '白色塑料',
        category: 'plastic',
        properties: { color: '#FFFFFF', metalness: 0.0, roughness: 0.5, opacity: 1.0 }
      },
      {
        id: 'plastic-black',
        name: '黑色塑料',
        category: 'plastic',
        properties: { color: '#000000', metalness: 0.0, roughness: 0.6, opacity: 1.0 }
      },
      
      // 木材材质
      {
        id: 'wood-oak',
        name: '橡木',
        category: 'wood',
        properties: { color: '#DEB887', metalness: 0.0, roughness: 0.8, opacity: 1.0 }
      },
      
      // 石材材质
      {
        id: 'stone-marble',
        name: '大理石',
        category: 'stone',
        properties: { color: '#F8F8FF', metalness: 0.0, roughness: 0.1, opacity: 1.0 }
      },
      
      // 玻璃材质
      {
        id: 'glass-clear',
        name: '透明玻璃',
        category: 'glass',
        properties: { color: '#FFFFFF', metalness: 0.0, roughness: 0.0, opacity: 0.3 }
      },
      
      // 程序化材质
      {
        id: 'procedural-noise',
        name: '噪声纹理',
        category: 'procedural',
        properties: { color: '#FFFFFF', metalness: 0.0, roughness: 0.5, opacity: 1.0 },
        proceduralType: 'noise',
        proceduralOptions: { scale: 5.0, octaves: 4 }
      },
      {
        id: 'procedural-wood',
        name: '程序化木纹',
        category: 'procedural',
        properties: { color: '#8B4513', metalness: 0.0, roughness: 0.8, opacity: 1.0 },
        proceduralType: 'wood',
        proceduralOptions: { scale: 2.0, rings: 15.0 }
      },
      {
        id: 'procedural-marble',
        name: '程序化大理石',
        category: 'procedural',
        properties: { color: '#F8F8FF', metalness: 0.0, roughness: 0.1, opacity: 1.0 },
        proceduralType: 'marble',
        proceduralOptions: { scale: 3.0, turbulence: 0.7 }
      }
    ];
  }

  /**
   * 从场景中提取材质信息
   */
  extractMaterialsFromScene(scene: THREE.Object3D): MaterialInfo[] {
    const materials: MaterialInfo[] = [];
    this.materials.clear();

    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        const material = Array.isArray(object.material) 
          ? object.material[0] 
          : object.material;

        if (material instanceof THREE.MeshStandardMaterial) {
          const materialName = material.name || `Material_${object.name}`;
          const isEditable = materialName.startsWith('Editable');

          const materialInfo: MaterialInfo = {
            name: materialName,
            meshName: object.name,
            isEditable,
            originalMaterial: material.clone(),
            currentMaterial: material,
            properties: {
              color: `#${material.color.getHexString()}`,
              metalness: material.metalness,
              roughness: material.roughness,
              opacity: material.opacity
            }
          };

          materials.push(materialInfo);
          this.materials.set(materialName, materialInfo);
        }
      }
    });

    return materials.filter(m => m.isEditable); // 只返回可编辑的材质
  }

  /**
   * 获取材质预设
   */
  getPresets(): MaterialPreset[] {
    return this.presets;
  }

  /**
   * 按类别获取预设
   */
  getPresetsByCategory(category: string): MaterialPreset[] {
    return this.presets.filter(preset => preset.category === category);
  }

  /**
   * 应用预设材质
   */
  applyPreset(materialName: string, presetId: string, scene: THREE.Object3D): boolean {
    const materialInfo = this.materials.get(materialName);
    if (!materialInfo || !materialInfo.isEditable) {
      console.warn(`Material ${materialName} is not editable or not found`);
      return false;
    }

    const preset = this.presets.find(p => p.id === presetId);
    if (!preset) {
      console.warn(`Preset ${presetId} not found`);
      return false;
    }

    // 创建新材质
    let newMaterial: THREE.Material;

    if (preset.proceduralType) {
      // 创建程序化材质
      newMaterial = this.createProceduralMaterial(preset);
    } else {
      // 创建标准材质
      newMaterial = new THREE.MeshStandardMaterial({
        color: new THREE.Color(preset.properties.color),
        metalness: preset.properties.metalness,
        roughness: preset.properties.roughness,
        transparent: preset.properties.opacity < 1,
        opacity: preset.properties.opacity
      });
    }

    // 应用到场景中的对应网格
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && 
          object.material && 
          object.name === materialInfo.meshName) {
        
        // 清理旧材质
        if (object.material !== materialInfo.originalMaterial) {
          (object.material as THREE.Material).dispose();
        }
        
        object.material = newMaterial;
        materialInfo.currentMaterial = newMaterial;
        materialInfo.properties = { ...preset.properties };
      }
    });

    return true;
  }

  /**
   * 创建程序化材质
   */
  private createProceduralMaterial(preset: MaterialPreset): THREE.Material {
    const options = preset.proceduralOptions || {};
    
    switch (preset.proceduralType) {
      case 'noise':
        return ProceduralMaterialFactory.createNoiseMaterial({
          scale: options.scale as number || 5.0,
          octaves: options.octaves as number || 4,
          color1: new THREE.Color(preset.properties.color),
          color2: new THREE.Color('#000000')
        });
        
      case 'wood':
        return ProceduralMaterialFactory.createWoodMaterial({
          scale: options.scale as number || 2.0,
          rings: options.rings as number || 15.0,
          color1: new THREE.Color(preset.properties.color),
          color2: new THREE.Color('#DEB887')
        });
        
      case 'marble':
        return ProceduralMaterialFactory.createMarbleMaterial({
          scale: options.scale as number || 3.0,
          turbulence: options.turbulence as number || 0.7,
          color1: new THREE.Color(preset.properties.color),
          color2: new THREE.Color('#888888')
        });
        
      case 'voronoi':
        return ProceduralMaterialFactory.createVoronoiMaterial({
          scale: options.scale as number || 5.0,
          color1: new THREE.Color(preset.properties.color),
          color2: new THREE.Color('#000000')
        });
        
      default:
        return new THREE.MeshStandardMaterial({
          color: new THREE.Color(preset.properties.color)
        });
    }
  }

  /**
   * 重置材质到原始状态
   */
  resetMaterial(materialName: string, scene: THREE.Object3D): boolean {
    const materialInfo = this.materials.get(materialName);
    if (!materialInfo || !materialInfo.isEditable) {
      return false;
    }

    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && 
          object.material && 
          object.name === materialInfo.meshName) {
        
        // 清理当前材质（如果不是原始材质）
        if (object.material !== materialInfo.originalMaterial) {
          (object.material as THREE.Material).dispose();
        }
        
        object.material = materialInfo.originalMaterial.clone();
        materialInfo.currentMaterial = object.material;
      }
    });

    return true;
  }

  /**
   * 获取可编辑材质列表
   */
  getEditableMaterials(): MaterialInfo[] {
    return Array.from(this.materials.values()).filter(m => m.isEditable);
  }

  /**
   * 清理资源
   */
  dispose() {
    this.proceduralMaterials.forEach(material => material.dispose());
    this.proceduralMaterials.clear();
    this.materials.clear();
  }
}

/**
 * 跨平台路径工具函数
 * 处理不同操作系统之间的路径差异
 */

import { platform } from 'os';

/**
 * 检测当前操作系统
 */
export const isWindows = (): boolean => platform() === 'win32';
export const isMacOS = (): boolean => platform() === 'darwin';
export const isLinux = (): boolean => platform() === 'linux';

/**
 * 获取路径分隔符
 */
export const getPathSeparator = (): string => isWindows() ? '\\' : '/';

/**
 * 标准化路径分隔符
 * 将所有路径分隔符统一为当前系统的标准分隔符
 */
export const normalizePath = (path: string): string => {
  if (!path) return path;
  
  // 统一使用正斜杠，因为Node.js和现代浏览器都支持
  return path.replace(/\\/g, '/');
};

/**
 * 连接路径片段
 * 跨平台安全的路径连接
 */
export const joinPath = (...segments: string[]): string => {
  if (segments.length === 0) return '';
  
  // 过滤空字符串和undefined
  const validSegments = segments.filter(segment => segment && typeof segment === 'string');
  
  if (validSegments.length === 0) return '';
  
  // 使用正斜杠连接，然后标准化
  const joined = validSegments.join('/');
  return normalizePath(joined);
};

/**
 * 获取文件扩展名
 */
export const getFileExtension = (filename: string): string => {
  if (!filename) return '';
  
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === 0) return '';
  
  return filename.slice(lastDotIndex + 1).toLowerCase();
};

/**
 * 获取文件名（不包含扩展名）
 */
export const getFileNameWithoutExtension = (filename: string): string => {
  if (!filename) return '';
  
  const lastDotIndex = filename.lastIndexOf('.');
  const lastSlashIndex = Math.max(filename.lastIndexOf('/'), filename.lastIndexOf('\\'));
  
  const nameStart = lastSlashIndex + 1;
  const nameEnd = lastDotIndex > lastSlashIndex ? lastDotIndex : filename.length;
  
  return filename.slice(nameStart, nameEnd);
};

/**
 * 获取目录路径
 */
export const getDirectoryPath = (filePath: string): string => {
  if (!filePath) return '';
  
  const normalizedPath = normalizePath(filePath);
  const lastSlashIndex = normalizedPath.lastIndexOf('/');
  
  if (lastSlashIndex === -1) return '';
  
  return normalizedPath.slice(0, lastSlashIndex);
};

/**
 * 检查路径是否为绝对路径
 */
export const isAbsolutePath = (path: string): boolean => {
  if (!path) return false;
  
  if (isWindows()) {
    // Windows: C:\ 或 \\server\share
    return /^[a-zA-Z]:\\/.test(path) || path.startsWith('\\\\');
  } else {
    // Unix-like: /path
    return path.startsWith('/');
  }
};

/**
 * 将相对路径转换为绝对路径
 */
export const resolveRelativePath = (basePath: string, relativePath: string): string => {
  if (isAbsolutePath(relativePath)) {
    return normalizePath(relativePath);
  }
  
  return joinPath(basePath, relativePath);
};

/**
 * 清理文件名，移除不安全的字符
 */
export const sanitizeFileName = (filename: string): string => {
  if (!filename) return '';
  
  // 移除或替换不安全的字符
  let sanitized = filename
    .replace(/[<>:"/\\|?*]/g, '_') // Windows不允许的字符
    // 使用Unicode属性转义来匹配控制字符
    .replace(/[\p{Cc}\p{Cf}\p{Co}\p{Cs}]/gu, '_') // 控制字符、格式字符、私有使用区域、代理对
    .replace(/^\.+/, '_') // 不能以点开头
    .replace(/\.+$/, '_') // 不能以点结尾
    .replace(/\s+/g, '_') // 空格替换为下划线
    .replace(/_+/g, '_'); // 多个下划线合并为一个
  
  // 限制长度
  if (sanitized.length > 255) {
    const ext = getFileExtension(sanitized);
    const nameWithoutExt = getFileNameWithoutExtension(sanitized);
    const maxNameLength = 255 - ext.length - 1; // -1 for the dot
    sanitized = nameWithoutExt.slice(0, maxNameLength) + '.' + ext;
  }
  
  return sanitized;
};

/**
 * 创建安全的URL路径
 */
export const createSafeUrlPath = (path: string): string => {
  if (!path) return '';
  
  return path
    .split('/')
    .map(segment => encodeURIComponent(segment))
    .join('/');
};

/**
 * 检查文件路径是否安全（防止路径遍历攻击）
 */
export const isSafePath = (path: string, allowedBasePath?: string): boolean => {
  if (!path) return false;
  
  const normalizedPath = normalizePath(path);
  
  // 检查是否包含路径遍历字符
  if (normalizedPath.includes('../') || normalizedPath.includes('..\\')) {
    return false;
  }
  
  // 如果指定了基础路径，检查是否在允许的范围内
  if (allowedBasePath) {
    const normalizedBasePath = normalizePath(allowedBasePath);
    const resolvedPath = resolveRelativePath(normalizedBasePath, normalizedPath);
    return resolvedPath.startsWith(normalizedBasePath);
  }
  
  return true;
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 获取MIME类型基于文件扩展名
 */
export const getMimeType = (filename: string): string => {
  const ext = getFileExtension(filename);
  
  const mimeTypes: Record<string, string> = {
    // 3D模型
    'glb': 'model/gltf-binary',
    'gltf': 'model/gltf+json',
    
    // 图片
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'webp': 'image/webp',
    'gif': 'image/gif',
    'svg': 'image/svg+xml',
    
    // 文档
    'pdf': 'application/pdf',
    'txt': 'text/plain',
    'json': 'application/json',
    
    // 压缩文件
    'zip': 'application/zip',
    'rar': 'application/x-rar-compressed',
    '7z': 'application/x-7z-compressed'
  };
  
  return mimeTypes[ext] || 'application/octet-stream';
};

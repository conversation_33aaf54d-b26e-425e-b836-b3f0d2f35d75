import { useState, useCallback, useEffect } from 'react';
import { SearchBox } from '../../components/search-box';
import { PrimaryButton } from '../../components/primary-button/primary-button';
import { IconButton } from '../../components/icon-button/icon-button';
import { Loading } from '../../components/loading/loading';
import { Modal } from '../../components/modal/modal';
import { InputBox } from '../../components/input-box/input-box';
import { Tag } from '../../components/tag/tag';
import { FileUpload, ImageUpload } from '../../components/upload';
import { FormGroup } from '../../components/form/form';
import { ConfirmDialog } from '../../components/confirm-dialog/confirm-dialog';
import { Pagination } from '../../components/pagination/pagination';
import { Trash2, Edit, Plus, Upload } from 'lucide-react';
import './AdminTable.css';
import './ModelModal.css';

import { apiService } from '../../services/api';
import type { ModelData } from '../../services/api';
import { FILE_VALIDATION_CONFIGS } from '../../utils/fileUpload';
import { useDataFetching } from '../../hooks/useDataFetching';
import { useSearch } from '../../hooks/useSearch';
import { useConfirmDialog } from '../../hooks/useConfirmDialog';

// 使用从API服务导入的ModelData接口

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const ModelManagement = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedModel, setSelectedModel] = useState<ModelData | null>(null);
  
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // New state for uploads
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadMessage, setUploadMessage] = useState('');

  // 使用数据获取Hook
  const { data: models, loading, addItem: addModel, removeItem: removeModel, updateItem: updateModel } = useDataFetching<ModelData>({
    fetchFn: useCallback(() => apiService.getModels(), []),
    sortFn: useCallback((a: ModelData, b: ModelData) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(), [])
  });

  // 使用搜索Hook
  const { query: searchQuery, setQuery: setSearchQuery, filteredData: filteredModels } = useSearch(models, {
    searchFields: ['name']
  });

  // 分页计算
  const totalItems = filteredModels.length;
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedModels = filteredModels.slice(startIndex, endIndex);

  // 搜索查询变化时重置页码
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  // 分页处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // 使用确认对话框Hook
  const { dialogState, showConfirm } = useConfirmDialog();

  const handleDeleteClick = async (model: ModelData) => {
    await showConfirm(
      {
        title: '删除模型',
        message: `确定要删除模型"${model.name}"吗？此操作不可撤销。`,
        type: 'warning',
        confirmText: '删除',
        confirmVariant: 'danger'
      },
      async () => {
        await apiService.deleteModel(model.id.toString());
        removeModel(model.id);
      }
    );
  };

    const handleSaveModel = useCallback(async (name: string, modelFile: File | null, thumbnailFile: File | null) => {
    if (!modelFile || !thumbnailFile) {
      alert('模型文件和缩略图均为必填项。');
      return;
    }

    setUploading(true);
    try {
      // 1. 上传模型文件
      setUploadMessage('正在上传模型文件...');
      setUploadProgress(0);
      const modelUploadResult = await apiService.uploadFile(modelFile, setUploadProgress);

      // 2. 上传缩略图文件
      setUploadMessage('正在上传缩略图...');
      setUploadProgress(0);
      const thumbnailUploadResult = await apiService.uploadFile(thumbnailFile, setUploadProgress);

      // 3. 创建模型记录
      setUploadMessage('正在创建模型记录...');
      const newModelData = {
        name,
        filePath: modelUploadResult.pathname, // Use pathname for DB
        thumbnailPath: thumbnailUploadResult.pathname, // Use pathname for DB
        size: modelUploadResult.size, // Pass the model size
      };

      const newModel = await apiService.addModel(newModelData);

      if (newModel) {
        addModel(newModel);
        setShowAddModal(false);
      } else {
        throw new Error('API未能返回创建后的模型数据。');
      }
    } catch (error) {
      console.error('添加模型失败:', error);
      alert(`添加模型失败: ${error instanceof Error ? error.message : '未知错误'}`);
      // Here you might want to add cleanup logic to delete uploaded files if the model creation fails
    } finally {
      setUploading(false);
      setUploadProgress(0);
      setUploadMessage('');
    }
  }, [addModel]);

  const handleUpdateModel = useCallback(async (name: string, _modelFile: File | null, thumbnailFile: File | null, existingModel?: ModelData) => {
    if (!existingModel) return;

    setUploading(true);
    try {
      let thumbnailBlobUrl: string | undefined | null = existingModel.thumbnailPath;

      // 如果有新的缩略图文件，则上传
      if (thumbnailFile) {
        setUploadMessage('正在上传新缩略图...');
        setUploadProgress(0);
        const thumbnailPath = `models/${existingModel.id}/thumbnail.png`;
        const thumbnailBlob = await apiService.uploadFile(thumbnailFile, setUploadProgress, thumbnailPath);
        thumbnailBlobUrl = thumbnailBlob.url;
      }

      // 更新模型名称和/或缩略图路径
      setUploadMessage('正在更新模型信息...');
      const updatedModel = await apiService.updateModel(existingModel.id.toString(), {
        name,
        thumbnailPath: thumbnailBlobUrl,
      });

      if (updatedModel) {
        updateModel(updatedModel.id, () => updatedModel);
        setSelectedModel(null); // 关闭编辑模态框
      } else {
        throw new Error('API未能返回更新后的模型数据。');
      }
    } catch (error) {
      console.error('更新模型失败:', error);
      alert(`更新模型失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setUploading(false);
      setUploadMessage('');
      setUploadProgress(0);
    }
  }, [updateModel]);



  if (loading) {
    return (
      <div className="loading-container">
        <Loading
          text="正在加载模型列表..."
          centered={true}
        />
      </div>
    );
  }

  return (
    <div className="model-management">
      <div className="management-toolbar">
        <SearchBox
          placeholder="搜索模型"
          value={searchQuery}
          onChange={setSearchQuery}
          className="search-box--management"
        />
        <div className="toolbar-actions">
          <PrimaryButton icon={Plus} onClick={() => setShowAddModal(true)}>
            添加模型
          </PrimaryButton>
        </div>
      </div>
      
      <div className="admin-table-container">
        <table className="admin-table">
          <thead>
            <tr>
              <th>缩略图</th>
              <th>模型名称</th>
              <th>文件类型</th>
              <th>文件大小</th>
              <th>上传时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {paginatedModels.map(model => (
              <tr key={model.id} className="model-row">
                <td className="thumbnail-cell">
                  {model.thumbnailPath ? (
                    <img 
                      src={model.thumbnailPath} 
                      alt={model.name}
                      className="thumbnail-image"
                    />
                  ) : (
                    <div className="thumbnail-placeholder">
                      <Upload size={20} />
                    </div>
                  )}
                </td>
                <td className="model-name">{model.name}</td>
                <td className="file-type">
                  <Tag variant="default" size="small">{model.fileType || 'N/A'}</Tag>
                </td>
                <td className="file-size">
                  {(() => {
                    const sizeInBytes = typeof model.size === 'number' ? model.size : Number(model.size);
                    if (!sizeInBytes || isNaN(sizeInBytes)) return 'N/A';
                    return `${(sizeInBytes / 1024 / 1024).toFixed(2)} MB`;
                  })()}
                </td>
                <td className="date-cell">{formatDate(model.createdAt)}</td>
                <td className="actions-cell">
                  <div className="action-buttons">
                    <IconButton icon={Edit} size="small" onClick={() => setSelectedModel(model)} />
                  <IconButton icon={Trash2} size="small" onClick={() => handleDeleteClick(model)} />
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* 分页器 */}
      {totalItems > 0 && (
        <Pagination
          current={currentPage}
          total={totalItems}
          pageSize={pageSize}
          onChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          showSizeChanger={true}
          pageSizeOptions={[10, 20, 50]}
          showTotal={true}
        />
      )}
      
      {showAddModal && (
        <ModelModal 
          onClose={() => setShowAddModal(false)} 
          onSave={handleSaveModel}
          uploading={uploading}
          uploadProgress={uploadProgress}
          uploadMessage={uploadMessage}
        />
      )}
      
      {selectedModel && (
        <ModelModal 
          model={selectedModel}
          onClose={() => setSelectedModel(null)} 
          onSave={handleUpdateModel}
          uploading={uploading}
          uploadProgress={uploadProgress}
          uploadMessage={uploadMessage}
        />
      )}

      <ConfirmDialog
        visible={dialogState.visible}
        title={dialogState.title}
        message={dialogState.message}
        type={dialogState.type}
        confirmText={dialogState.confirmText}
        confirmVariant={dialogState.confirmVariant}
        showCancel={dialogState.showCancel}
        onConfirm={dialogState.onConfirm}
        onCancel={dialogState.onCancel}
      />
    </div>
  );
};

interface ModelModalProps {
  model?: ModelData;
  onClose: () => void;
  onSave: (name: string, modelFile: File | null, thumbnailFile: File | null, existingModelData?: ModelData) => void | Promise<void>;
  uploading: boolean;
  uploadProgress: number;
  uploadMessage: string;
}

// 模型添加/编辑弹窗
const ModelModal = ({ 
  model, 
  onClose, 
  onSave,
  uploading,
  uploadProgress,
  uploadMessage 
}: ModelModalProps) => {
  const [name, setName] = useState(model?.name || '');
  const [modelFile, setModelFile] = useState<File | null>(null);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(model?.thumbnailPath || null);

  const handleModelFile = (file: File) => {
    setModelFile(file);
    if (!model) { // Only update name if it's a new model
      const baseName = file.name.replace(/\.[^/.]+$/, '');
      setName(baseName);
    }
  };
  
  const handleThumbnailFile = (file: File) => {
    setThumbnailFile(file);
    const previewUrl = URL.createObjectURL(file);
    setThumbnailPreview(previewUrl);
  };

  const handleRemoveModelFile = () => {
    setModelFile(null);
  };

  const handleRemoveThumbnail = () => {
    if (thumbnailPreview && thumbnailPreview.startsWith('blob:')) {
      URL.revokeObjectURL(thumbnailPreview);
    }
    setThumbnailFile(null);
    setThumbnailPreview(model?.thumbnailPath || null);
  };

  const handleError = (error: string) => {
    alert(error);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!model && !modelFile) {
      alert('请选择一个模型文件。');
      return;
    }

    // Awaiting the onSave promise ensures that any errors thrown during the
    // upload or save process are properly handled and not lost as unhandled
    // promise rejections. The onSave function itself contains the
    // necessary try/catch logic to inform the user of the outcome.
    await onSave(name, modelFile, thumbnailFile, model);
  };
  
  const handleSave = () => {
    if (!uploading) {
      const event = new Event('submit') as unknown as React.FormEvent<HTMLFormElement>;
      handleSubmit(event);
    }
  };

  return (
    <Modal
      visible={true}
      title={model ? '编辑模型' : '添加新模型'}
      onClose={onClose}
      size="medium"
      showCloseButton={!uploading}
      closeOnOverlayClick={!uploading}
      footer={
        <PrimaryButton onClick={handleSave} disabled={uploading} showIcon={false}>
          {uploading ? '上传中...' : '保存'}
        </PrimaryButton>
      }
    >
      <div className={`model-modal-form ${uploading ? 'form--disabled' : ''}`}>
        <FormGroup label="上传模型" required>
          <FileUpload
            onFileSelect={handleModelFile}
            validationOptions={FILE_VALIDATION_CONFIGS.GLB_ONLY}
            onError={handleError}
            accept=".glb"
            placeholder="点击或拖拽模型文件到此处上传"
            formatHint="支持格式：glb、gltf"
            selectedFileName={modelFile ? modelFile.name : (model && !modelFile ? `${model.name}.glb` : undefined)}
            selectedFileSize={modelFile ? modelFile.size : (model && !modelFile && model.size ? model.size : undefined)}
            disabled={uploading}
            showRemoveButton={!!modelFile}
            onRemove={handleRemoveModelFile}
          />
        </FormGroup>

        <FormGroup label="上传缩略图">
          <ImageUpload
            onFileSelect={handleThumbnailFile}
            validationOptions={FILE_VALIDATION_CONFIGS.IMAGE}
            onError={handleError}
            accept="image/png, image/jpeg, image/webp"
            previewUrl={thumbnailPreview}
            disabled={uploading}
            placeholder="点击上传缩略图"
            showRemoveButton={!!thumbnailFile}
            onRemove={handleRemoveThumbnail}
          />
        </FormGroup>

        <FormGroup label="模型名称" required>
          <InputBox
            type="text"
            value={name}
            onChange={setName}
            placeholder="输入模型名称"
            fullWidth
          />
        </FormGroup>

        {uploading && (
          <div className="model-modal-upload-status">
            <p className="upload-message">{uploadMessage}</p>
            <div className="progress-bar-container">
              <div
                className="progress-bar"
                style={{ '--progress-width': `${uploadProgress}%` } as React.CSSProperties}
              ></div>
            </div>
            <span className="upload-percentage">{`${Math.round(uploadProgress)}%`}</span>
          </div>
        )}

      </div>
    </Modal>
  );
};

export default ModelManagement;
/* 确认对话框样式 */
.confirm-dialog {
  padding: var(--spacing-lg);
}

.confirm-dialog__content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.confirm-dialog__icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--icon-container-size);
  height: var(--icon-container-size);
  border-radius: 50%;
  margin-top: var(--spacing-xxs);
}

.confirm-dialog__icon-svg {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
}

/* 图标颜色变体 */
.confirm-dialog--info .confirm-dialog__icon {
  background-color: var(--color-brand-alpha);
}

.confirm-dialog--info .confirm-dialog__icon-svg--info {
  color: var(--color-brand);
}

.confirm-dialog--warning .confirm-dialog__icon {
  background-color: var(--color-warning-alpha);
}

.confirm-dialog--warning .confirm-dialog__icon-svg--warning {
  color: var(--color-warning);
}

.confirm-dialog--error .confirm-dialog__icon {
  background-color: var(--color-error-alpha);
}

.confirm-dialog--error .confirm-dialog__icon-svg--error {
  color: var(--color-error);
}

.confirm-dialog--success .confirm-dialog__icon {
  background-color: var(--color-success-alpha);
}

.confirm-dialog--success .confirm-dialog__icon-svg--success {
  color: var(--color-success);
}

.confirm-dialog__message {
  flex: 1;
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  color: var(--color-content-primary);
}

.confirm-dialog__actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .confirm-dialog__content {
    flex-direction: column;
    text-align: center;
  }
  
  .confirm-dialog__icon {
    align-self: center;
  }
  
  .confirm-dialog__actions {
    flex-direction: column;
  }
  
  .confirm-dialog__actions button {
    width: 100%;
  }
}

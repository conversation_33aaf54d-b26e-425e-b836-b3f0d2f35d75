/* 管理表格特定样式 - 基础样式已在components.css中定义 */

/* 工具栏 */
.admin-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-base);
}

/* 管理页面工具栏 - 搜索框和添加按钮在同一行 */
.management-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-base);
}

.management-toolbar .toolbar-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 管理表格特定的列样式 - 基础样式已在components.css中定义 */
.admin-table .thumbnail-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--radius-sm);
}

.admin-table .thumbnail-placeholder {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  color: var(--color-content-secondary);
}

.admin-table .action-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 管理表格特定的表单样式 - 基础样式已在components.css中定义 */
.material-preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

/* 管理表格特定的预览样式覆盖 */
.admin-table .preview-header {
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
}

.admin-table .preview-sphere {
  width: 100px;
  height: 100px;
}

/* 模型上传和缩略图上传区域布局优化 */
.model-upload-group {
  margin-bottom: var(--spacing-md);
}

.thumbnail-upload-group {
  margin-bottom: var(--spacing-md);
}

.thumbnail-uploader {
  width: var(--spacing-giant);
  height: var(--spacing-giant);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.2s, background-color 0.2s;
  background-color: var(--color-bg-overlay);
  overflow: hidden;
}

.thumbnail-uploader:hover,
.thumbnail-uploader.drag-over {
  border-color: var(--color-brand);
  background-color: var(--color-bg-hover);
}

.thumbnail-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder-icon {
  color: var(--color-content-secondary);
}

/* 当前缩略图样式 */
.current-thumbnail {
  width: var(--spacing-xxl);
  vertical-align: middle;
  margin-left: var(--spacing-sm);
}

/* 颜色预览样式 */
.color-preview {
  width: var(--spacing-xl);
  height: var(--spacing-xl);
  border-radius: var(--radius-base);
  border: 1px solid var(--color-border);
  background-color: var(--preview-color, var(--color-content-mute));
}

/* 管理表格特定的材质预览样式 */
.material-preview {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.material-preview-sphere {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid var(--color-border);
  overflow: hidden;
  flex-shrink: 0;
}

/* 进度条样式 */
.progress-bar-container {
  width: 100%;
  height: var(--spacing-xs);
  background-color: var(--color-bg-overlay);
  border-radius: var(--radius-xs);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--color-brand);
  width: var(--progress-width, 0%);
  border-radius: var(--radius-xs);
  transition: width 0.3s ease;
}
import React from 'react';
import './form.css';

export interface FormGroupProps {
  /** 标签文本 */
  label?: string;
  /** 是否必填 */
  required?: boolean;
  /** 错误信息 */
  error?: string;
  /** 帮助文本 */
  helpText?: string;
  /** 子组件 */
  children: React.ReactNode;
  /** 自定义类名 */
  className?: string;
}

export const FormGroup: React.FC<FormGroupProps> = ({
  label,
  error,
  helpText,
  children,
  className = ''
}) => {
  const containerClasses = [
    'form-group',
    error ? 'form-group--error' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {label && (
        <label className="form-group__label">
          {label}
        </label>
      )}
      <div className="form-group__control">
        {children}
      </div>
      {error && (
        <div className="form-group__error">{error}</div>
      )}
      {helpText && !error && (
        <div className="form-group__help">{helpText}</div>
      )}
    </div>
  );
};

export interface FormProps {
  /** 表单内容 */
  children: React.ReactNode;
  /** 提交处理函数 */
  onSubmit?: (e: React.FormEvent<HTMLFormElement>) => void;
  /** 自定义类名 */
  className?: string;
  /** 是否禁用表单 */
  disabled?: boolean;
}

export const Form: React.FC<FormProps> = ({
  children,
  onSubmit,
  className = '',
  disabled = false
}) => {
  const formClasses = [
    'form',
    disabled ? 'form--disabled' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <form className={formClasses} onSubmit={onSubmit}>
      <fieldset disabled={disabled} className="form__fieldset">
        {children}
      </fieldset>
    </form>
  );
};

export interface FormActionsProps {
  /** 操作按钮 */
  children: React.ReactNode;
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right';
  /** 自定义类名 */
  className?: string;
}

export const FormActions: React.FC<FormActionsProps> = ({
  children,
  align = 'right',
  className = ''
}) => {
  const containerClasses = [
    'form-actions',
    `form-actions--${align}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
};

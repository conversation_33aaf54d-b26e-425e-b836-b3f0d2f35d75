/* 页面布局样式 */
.page-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--color-bg-primary);
}

/* 页面头部 */
.page-header {
  flex-shrink: 0;
  background-color: var(--color-bg-primary);
}

.page-header--with-divider {
  border-bottom: var(--border-width) solid var(--color-border);
}

.page-header__content {
  padding: var(--spacing-lg) var(--spacing-xl);
}

.page-header__info {
  margin-bottom: var(--spacing-md);
}

.page-header__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-content-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: var(--line-height-tight);
}

.page-header__description {
  font-size: var(--font-size-base);
  color: var(--color-content-secondary);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

.page-header__toolbar {
  padding: 0 var(--spacing-xl) var(--spacing-lg) var(--spacing-xl);
}

/* 页面内容 */
.page-content {
  flex: 1;
  overflow: auto;
  background-color: var(--color-bg-primary);
}

.page-content--none {
  padding: 0;
}

.page-content--small {
  padding: var(--spacing-sm);
}

.page-content--medium {
  padding: var(--spacing-lg) var(--spacing-xl);
}

.page-content--large {
  padding: var(--spacing-xl) var(--spacing-xxxl);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header__content {
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .page-header__toolbar {
    padding: 0 var(--spacing-lg) var(--spacing-md) var(--spacing-lg);
  }
  
  .page-header__title {
    font-size: var(--font-size-lg);
  }
  
  .page-content--medium {
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .page-content--large {
    padding: var(--spacing-lg) var(--spacing-xl);
  }
}

@media (max-width: 480px) {
  .page-header__content {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .page-header__toolbar {
    padding: 0 var(--spacing-md) var(--spacing-sm) var(--spacing-md);
  }
  
  .page-header__title {
    font-size: var(--font-size-base);
  }
  
  .page-content--medium {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .page-content--large {
    padding: var(--spacing-md) var(--spacing-lg);
  }
}

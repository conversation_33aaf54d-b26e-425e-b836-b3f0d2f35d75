/* 输入框组件样式 - 独立样式系统 */
.input-box-container {
  display: flex;
  align-items: center;
  background-color: var(--color-bg-overlay);
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--radius-base);
  transition: var(--transition-base);
}

.input-box-container.focused {
  border-color: var(--color-brand);
}

.input-box-container.disabled {
  opacity: var(--opacity-disabled);
  cursor: not-allowed;
}

/* 大小变体 */
.input-box-container.small {
  height: var(--input-height-small);
}

.input-box-container.medium {
  height: var(--input-height-medium);
}

.input-box-container.large {
  height: var(--input-height-large);
}

/* 宽度变体 */
.input-box-container--narrow {
  width: var(--input-width-narrow);
}

.input-box-container--default {
  width: var(--input-width-default);
}

.input-box-container--wide {
  width: var(--input-width-wide);
}

.input-box-container--full {
  width: 100%;
}

.input-box {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--color-content-accent);
  font-size: var(--font-size-base);
  padding: 0 var(--spacing-base);
  outline: none;
  width: 100%;
  height: 100%;
}

.input-box::placeholder {
  color: var(--color-content-secondary);
}

.input-box.has-prefix {
  padding-left: 0;
}

.input-box.has-suffix {
  padding-right: 0;
}

.input-prefix-icon,
.input-suffix-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-base);
  color: var(--color-content-regular);
}

.input-box-container:hover:not(.disabled) {
  border-color: var(--color-content-secondary);
}

.input-box-container.disabled .input-box {
  cursor: not-allowed;
}
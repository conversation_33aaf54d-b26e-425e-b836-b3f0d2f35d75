import { useState, useCallback, useRef } from 'react';
import type { ModelData } from '../services/api';

export interface OriginalMaterial {
  name: string;
  meshName: string;
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  isEditable: boolean;
}

export interface MaterialProps {
  textureUrl?: string;
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
}

export type PerMeshMaterials = Record<string, MaterialProps>;

/**
 * 模型状态管理Hook
 * 统一管理模型、材质、上传等相关状态
 */
export const useModelState = () => {
  // 模型相关状态
  const [currentModel, setCurrentModel] = useState<ModelData | null>(null);
  const [selectedModelId, setSelectedModelId] = useState<string>('');
  
  // 上传相关状态
  const [uploadedModelUrl, setUploadedModelUrl] = useState<string | null>(null);
  const [showLoader, setShowLoader] = useState(false);
  
  // 材质相关状态
  const [originalMaterials, setOriginalMaterials] = useState<OriginalMaterial[]>([]);
  const [perMeshMaterials, setPerMeshMaterials] = useState<PerMeshMaterials>({});
  const [activeMaterialName, setActiveMaterialName] = useState<string | null>(null);
  const [materialsReady, setMaterialsReady] = useState(false);
  
  // 使用ref来避免闭包问题
  const activeMaterialNameRef = useRef<string | null>(null);

  // 重置所有状态
  const resetModelState = useCallback(() => {
    setCurrentModel(null);
    setSelectedModelId('');
    setUploadedModelUrl(null);
    setShowLoader(false);
    setOriginalMaterials([]);
    setPerMeshMaterials({});
    setActiveMaterialName(null);
    setMaterialsReady(false);
    activeMaterialNameRef.current = null;
  }, []);

  // 重置材质状态
  const resetMaterialState = useCallback(() => {
    setOriginalMaterials([]);
    setPerMeshMaterials({});
    setActiveMaterialName(null);
    setMaterialsReady(false);
    activeMaterialNameRef.current = null;
  }, []);

  // 设置当前模型
  const setCurrentModelWithReset = useCallback((model: ModelData | null) => {
    setCurrentModel(model);
    if (model) {
      resetMaterialState();
    }
  }, [resetMaterialState]);

  // 处理材质激活
  const handleMaterialActivate = useCallback((materialName: string) => {
    setActiveMaterialName(materialName);
    activeMaterialNameRef.current = materialName;
  }, []);

  // 处理原始材质提取
  const handleOriginalMaterialsExtracted = useCallback((materials: OriginalMaterial[]) => {
    setOriginalMaterials(materials);
    setMaterialsReady(true);
    
    // 如果有材质，默认激活第一个可编辑材质
    const editableMaterials = materials.filter(m => m.isEditable);
    if (editableMaterials.length > 0) {
      const defaultMaterialName = editableMaterials[0].name;
      setActiveMaterialName(defaultMaterialName);
      activeMaterialNameRef.current = defaultMaterialName;
    }
  }, []);

  // 清理上传的模型URL
  const cleanupUploadedModel = useCallback(() => {
    if (uploadedModelUrl) {
      URL.revokeObjectURL(uploadedModelUrl);
      setUploadedModelUrl(null);
    }
  }, [uploadedModelUrl]);

  return {
    // 状态
    currentModel,
    selectedModelId,
    uploadedModelUrl,
    showLoader,
    originalMaterials,
    perMeshMaterials,
    activeMaterialName,
    materialsReady,
    activeMaterialNameRef,
    
    // 设置器
    setCurrentModel: setCurrentModelWithReset,
    setSelectedModelId,
    setUploadedModelUrl,
    setShowLoader,
    setOriginalMaterials,
    setPerMeshMaterials,
    setActiveMaterialName,
    setMaterialsReady,
    
    // 方法
    resetModelState,
    resetMaterialState,
    handleMaterialActivate,
    handleOriginalMaterialsExtracted,
    cleanupUploadedModel
  };
};

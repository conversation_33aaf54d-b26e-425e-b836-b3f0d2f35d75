/* ===== 材质弹窗样式 ===== */
/* 基于全局组件样式，专注于材质弹窗特定的布局和交互 */

/* === 主容器布局 === */
.material-modal-form {
  display: flex;
  flex-direction: column;
  min-height: 500px;
  max-height: 85vh;
  gap: var(--spacing-lg);
}

/* 材质名称区域 - 单独一行在最上方 */
.material-name-section {
  width: 100%;
}

.material-modal-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  flex: 1;
  min-height: 0;
  align-items: start;
}

/* === 预览区域 === */
.material-modal-preview {
  /* 继承 .modal-form .form-group 样式 */
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.material-modal-preview .preview-header {
  /* 继承 .form-group label 样式 */
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-regular);
  margin-bottom: 2px;
}

.material-modal-preview .preview-sphere {
  width: 100%;
  aspect-ratio: 1; /* 保持正方形 */
  border-radius: var(--radius-lg);
  background: var(--color-bg-overlay);
  border: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 20px;
  box-sizing: border-box;
}

/* === 设置区域 === */
.material-modal-settings {
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow-y: auto;
}

/* 紧凑的材质设置样式 */
.compact-settings {
  gap: var(--spacing-sm);
}

.compact-settings .form-group {
  margin-bottom: 0;
}

.compact-settings .form-group:last-child {
  margin-bottom: 0;
}



/* === 操作按钮区域 === */
.material-modal-form .modal-actions {
  flex-shrink: 0;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  margin-top: var(--spacing-md);
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .material-modal-form {
    min-height: 400px;
    max-height: 95vh;
  }
  
  .material-modal-layout {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .material-modal-preview .preview-sphere {
    min-height: 180px;
  }
  
}

@media (max-width: 480px) {
  .material-modal-layout {
    gap: var(--spacing-base);
  }

}
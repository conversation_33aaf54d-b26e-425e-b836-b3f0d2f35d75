import React, { Component, type ReactNode } from 'react';
import { AlertTriangle } from 'lucide-react';
import { classifyError, type AppError } from '../../utils/errorHandling';
import { PrimaryButton } from '../primary-button/primary-button';
import './error-boundary.css';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onRetry?: () => void;
  onError?: (error: AppError) => void;
}

interface State {
  hasError: boolean;
  error?: AppError;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    const appError = classifyError(error);
    return { hasError: true, error: appError };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    const appError = classifyError(error);
    this.props.onError?.(appError);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="error-boundary">
          <div className="error-content">
            <AlertTriangle className="error-icon" size={48} />
            <h3 className="error-title">加载失败</h3>
            <p className="error-message">
              {this.state.error?.message || '模型加载时发生错误，请检查文件格式或网络连接'}
            </p>
            <PrimaryButton
              onClick={() => {
                this.setState({ hasError: false, error: undefined });
                this.props.onRetry?.();
              }}
              showIcon={false}
            >
              重试
            </PrimaryButton>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

import React, { Suspense } from 'react';
import { ErrorBoundary } from '../error-boundary/error-boundary';
import { Loading } from '../loading/loading';

export interface AsyncBoundaryProps {
  /** 子组件 */
  children: React.ReactNode;
  /** 加载状态显示的组件 */
  fallback?: React.ReactNode;
  /** 错误状态显示的组件 */
  errorFallback?: React.ReactNode;
  /** 加载文本 */
  loadingText?: string;
  /** 错误重试回调 */
  onRetry?: () => void;
}

/**
 * 异步边界组件
 * 结合了 Suspense 和 ErrorBoundary，提供完整的异步状态处理
 */
export const AsyncBoundary: React.FC<AsyncBoundaryProps> = ({
  children,
  fallback,
  errorFallback,
  loadingText = '加载中...',
  onRetry
}) => {
  const defaultFallback = fallback || (
    <Loading text={loadingText} variant="minimal" />
  );

  return (
    <ErrorBoundary fallback={errorFallback} onRetry={onRetry}>
      <Suspense fallback={defaultFallback}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
};

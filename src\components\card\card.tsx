import React from 'react';
import './card.css';

export interface CardProps {
  /** 卡片内容 */
  children: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 卡片变体 */
  variant?: 'default' | 'elevated' | 'outlined' | 'glass';
  /** 是否可点击 */
  clickable?: boolean;
  /** 点击事件 */
  onClick?: () => void;
  /** 是否显示悬停效果 */
  hoverable?: boolean;
  /** 内边距大小 */
  padding?: 'none' | 'small' | 'medium' | 'large';
  /** 圆角大小 */
  radius?: 'none' | 'small' | 'medium' | 'large';
}

export const Card: React.FC<CardProps> = ({
  children,
  className = '',
  variant = 'default',
  clickable = false,
  onClick,
  hoverable = false,
  padding = 'medium',
  radius = 'medium'
}) => {
  const containerClasses = [
    'card',
    `card--${variant}`,
    `card--padding-${padding}`,
    `card--radius-${radius}`,
    clickable ? 'card--clickable' : '',
    hoverable ? 'card--hoverable' : '',
    className
  ].filter(Boolean).join(' ');

  const handleClick = () => {
    if (clickable && onClick) {
      onClick();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (clickable && onClick && (e.key === 'Enter' || e.key === ' ')) {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <div
      className={containerClasses}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      role={clickable ? 'button' : undefined}
      tabIndex={clickable ? 0 : undefined}
    >
      {children}
    </div>
  );
};

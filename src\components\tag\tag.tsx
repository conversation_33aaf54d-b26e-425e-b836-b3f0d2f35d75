import React from 'react';
import './tag.css';

interface TagProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error';
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export const Tag: React.FC<TagProps> = ({
  children,
  variant = 'default',
  size = 'medium',
  className = ''
}) => {
  return (
    <span 
      className={`tag tag--${variant} tag--${size} ${className}`}
    >
      {children}
    </span>
  );
};
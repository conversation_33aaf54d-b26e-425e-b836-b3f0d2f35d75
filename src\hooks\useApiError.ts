import { useCallback } from 'react';
import { useNotification } from '../components/notification/notification';

/**
 * 通用的API错误处理Hook
 * 提供统一的错误处理和用户提示
 */
export const useApiError = () => {
  const notify = useNotification();

  const handleApiError = useCallback((
    error: unknown,
    context: string = '操作',
    customMessage?: string
  ) => {
    console.error(`${context}失败:`, error);
    
    let errorMessage = customMessage;
    
    if (!errorMessage) {
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else {
        errorMessage = `${context}失败，请重试`;
      }
    }
    
    notify(errorMessage, 'error');
  }, [notify]);

  const handleApiSuccess = useCallback((message: string) => {
    notify(message, 'success');
  }, [notify]);

  return {
    handleApiError,
    handleApiSuccess
  };
};

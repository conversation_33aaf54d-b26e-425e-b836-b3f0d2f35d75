.tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border-radius: var(--radius-base);
  white-space: nowrap;
  transition: all 0.2s ease-in-out;
}

/* Variants */
.tag--default {
  background-color: var(--color-bg-secondary);
  color: var(--color-content-regular);
  border: 1px solid var(--color-border-regular);
}

.tag--primary {
  background-color: var(--color-brand);
  color: var(--color-content-inverse);
  border: 1px solid var(--color-brand);
}

.tag--success {
  background-color: var(--color-success);
  color: var(--color-content-inverse);
  border: 1px solid var(--color-success);
}

.tag--warning {
  background-color: var(--color-warning);
  color: var(--color-content-inverse);
  border: 1px solid var(--color-warning);
}

.tag--error {
  background-color: var(--color-error);
  color: var(--color-content-inverse);
  border: 1px solid var(--color-error);
}

/* Sizes */
.tag--small {
  padding: 2px 6px;
  font-size: 11px;
  line-height: 1.2;
}

.tag--medium {
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1.3;
}

.tag--large {
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.4;
}
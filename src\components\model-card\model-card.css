/* 简化后的模型卡片组件样式 - 仅显示模型名称和图片 */
.model-card {
  display: flex;
  width: 100%;
  aspect-ratio: 1/1; /* 1:1 比例 */
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-sm);
  gap: var(--spacing-sm);
  border: var(--border-width) solid var(--card-border-default);
  background: linear-gradient(144deg, rgba(0, 0, 0, 0.30) 0%, rgba(255, 255, 255, 0.05) 98.73%);
  box-shadow: var(--card-shadow-inset);
  backdrop-filter: blur(var(--spacing-md));
  transition: var(--transition-base);
  cursor: pointer;
  overflow: hidden;
}

.model-card:hover {
  border-color: var(--card-border-hover);
  box-shadow: var(--card-shadow-hover);
  transform: translateY(calc(-1 * var(--spacing-xs)));
}

.model-card__image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.model-card__placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 1px dashed var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  text-align: center;
}

.model-card:hover .model-card__image {
  transform: scale(1.05);
}

.model-card__title {
  font-size: var(--font-size-base);
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 var(--spacing-md);
  width: 100%;
  text-align: center;
  order: 1;
}

.model-card__image-container {
  order: 2;
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  width: 100%;
  overflow: hidden;
}
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .model-card {
    width: 200px;
  }
}
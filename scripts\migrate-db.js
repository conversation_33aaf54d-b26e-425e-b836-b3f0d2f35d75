#!/usr/bin/env node

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.development';
const envPath = path.resolve(__dirname, `../${envFile}`);

// Try to load the environment-specific file first
if (fs.existsSync(envPath)) {
    dotenv.config({ path: envPath });
    console.log(`✅ Loaded environment from: ${envFile}`);
} else {
    // Fallback to .env file
    dotenv.config({ path: path.resolve(__dirname, '../.env') });
    console.log(`✅ Loaded environment from: .env (fallback)`);
}

async function migrateDatabase() {
    console.log('🚀 Starting database migration...');
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    
    // Check required environment variables
    const requiredVars = ['POSTGRES_PRISMA_URL', 'POSTGRES_URL_NON_POOLING'];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        console.error('❌ Missing required environment variables:');
        missingVars.forEach(varName => console.error(`   - ${varName}`));
        process.exit(1);
    }
    
    console.log('✅ All required environment variables are set');
    
    const prisma = new PrismaClient();
    
    try {
        // Generate Prisma client
        console.log('📦 Generating Prisma client...');
        const { execSync } = await import('child_process');
        execSync('npx prisma generate', { stdio: 'inherit' });
        
        // Push database schema
        console.log('🗄️  Pushing database schema...');
        execSync('npx prisma db push', { stdio: 'inherit' });
        
        console.log('✅ Database migration completed successfully!');
        
    } catch (error) {
        console.error('❌ Database migration failed:', error);
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

// Run migration
migrateDatabase().catch((error) => {
    console.error('❌ Migration script failed:', error);
    process.exit(1);
});
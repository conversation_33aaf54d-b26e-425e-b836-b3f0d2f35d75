import React, { useState, useEffect } from 'react';
import { Box, Paintbrush, Home } from 'lucide-react';
import { Loading } from '../../components/loading/loading';
import { Card } from '../../components/card/card';
import ModelManagement from './ModelManagement';
import MaterialManagement from './MaterialManagement';
import LogoPng from '../../assets/images/Logo.png';
import { apiService } from '../../services/api';
import './Dashboard.css';

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    // 检查用户是否已登录
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      window.location.href = '/admin';
    }
  }, []);


  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <Overview />;
      case 'models':
        return <ModelManagement />;
      case 'materials':
        return <MaterialManagement />;
      default:
        return <Overview />;
    }
  };

  return (
    <div className="dashboard-container theme-dark">
      <div className="dashboard-sidebar">
        <div className="sidebar-header">
          <div className="logo-container" onClick={() => window.location.href = '/'}>
            <img className="logo" src={LogoPng} alt="RINKO" />
            <div className="logo-tooltip">返回前台页面</div>
          </div>
          <h2>后台管理</h2>
        </div>
        
        <ul className="sidebar-menu">
          <li
            className={`menu-item ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            <Home size={16} />
            <span>总览</span>
          </li>
          <li
            className={`menu-item ${activeTab === 'models' ? 'active' : ''}`}
            onClick={() => setActiveTab('models')}
          >
            <Box size={16} />
            <span>模型管理</span>
          </li>
          <li
            className={`menu-item ${activeTab === 'materials' ? 'active' : ''}`}
            onClick={() => setActiveTab('materials')}
          >
            <Paintbrush size={16} />
            <span>材质管理</span>
          </li>
        </ul>
        
        {/* 移除了退出登录按钮 */}
        <div className="sidebar-footer">
          {/* 可以在这里添加其他底部内容 */}
        </div>
      </div>
      
      <div className="dashboard-content">
        <div className="content-header">
          <h1>{activeTab === 'overview' ? '总览' : activeTab === 'models' ? '模型管理' : '材质管理'}</h1>
          <div className="header-actions">
            {/* 移除了退出登录图标按钮 */}
          </div>
        </div>
        
        <div className="content-body">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

// 统计卡片复用组件
const StatCard = ({ icon, label, value }: { icon: React.ReactNode; label: string; value: number | string }) => (
  <Card variant="elevated" className="stat-card">
    <div className="stat-icon">{icon}</div>
    <div className="stat-content">
      <div className="stat-value">{value}</div>
      <div className="stat-label">{label}</div>
    </div>
  </Card>
);

// 总览组件：动态获取并展示真实统计数据
const Overview = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    modelCount: 0,
    materialCount: 0,
    visitors: 0,
    newModelCount: 0,
    newMaterialCount: 0,
    totalModelSize: 0,
  });

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const [models, materials] = await Promise.all([
          apiService.getModels(),
          apiService.getMaterials(),
        ]);

        const now = Date.now();
        const weekMs = 7 * 24 * 60 * 60 * 1000;

        const parseSize = (sizeStr?: string | number | null): number => {
          // The API might return size as a number (bytes) or a formatted string.
          // This function handles both cases to prevent runtime errors.
          if (!sizeStr) return 0;

          if (typeof sizeStr === 'number') {
            return sizeStr; // Assume it's already in bytes.
          }

          if (typeof sizeStr !== 'string') {
            return 0; // Cannot parse other types.
          }

          const match = sizeStr.match(/([\d.]+)\s*([KMG]B)/i);
          if (!match) return 0;
          
          const value = parseFloat(match[1]);
          const unit = match[2].toUpperCase();
          if (unit === 'KB') return value * 1024;
          if (unit === 'MB') return value * 1024 * 1024;
          if (unit === 'GB') return value * 1024 * 1024 * 1024;
          return 0;
        };

        const totalModelSizeBytes = models.reduce((sum, m) => sum + parseSize(m.size), 0);
        const toMB = (bytes: number) => bytes / (1024 * 1024);

        setStats({
          modelCount: models.length,
          materialCount: materials.length,
          visitors: 0,
          newModelCount: models.filter((m) => now - new Date(m.createdAt).getTime() < weekMs).length,
          newMaterialCount: materials.filter((mat) => now - new Date(mat.createdAt).getTime() < weekMs).length,
          totalModelSize: Number(toMB(totalModelSizeBytes).toFixed(1)),
        });
      } catch (error) {
        console.error('Failed to fetch dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return <Loading text="统计信息加载中..." centered={false} />;
  }

  return (
    <div className="overview-container">
      <div className="stats-row">
        <StatCard icon={<Box size={24} />} label="模型总数" value={stats.modelCount} />
        <StatCard icon={<Paintbrush size={24} />} label="材质总数" value={stats.materialCount} />
      </div>
    </div>
  );
};

export default Dashboard;

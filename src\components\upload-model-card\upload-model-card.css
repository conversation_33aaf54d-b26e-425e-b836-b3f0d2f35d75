/* 上传模型卡片样式 - 与模型卡片保持一致的尺寸和外观 */
.upload-model-card {
  display: flex;
  width: 100%;
  aspect-ratio: 1/1; /* 1:1 比例 */
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-sm);
  gap: var(--spacing-sm);
  border: 1px solid var(--card-border-default);
  background: linear-gradient(144deg, rgba(0, 0, 0, 0.30) 0%, rgba(255, 255, 255, 0.05) 98.73%);
  box-shadow: var(--card-shadow-inset);
  backdrop-filter: blur(var(--spacing-md));
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.upload-model-card:hover {
  border-color: var(--card-border-hover);
  box-shadow: var(--card-shadow-hover);
  transform: translateY(calc(-1 * var(--spacing-xs)));
}

/* 自定义上传区域样式 */
.upload-model-card__content {
  order: 2;
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-base);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.upload-model-card__icon {
  color: var(--text-color-secondary);
  transition: color 0.3s ease;
}

.upload-model-card__text {
  text-align: center;
  pointer-events: none;
}

.upload-model-card__primary {
  margin-bottom: 8px;
  font-size: var(--font-size-base);
}

.upload-model-card__secondary {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
}

.upload-model-card__input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
  opacity: 0;
  pointer-events: none;
}


/* 响应式设计 */
@media (max-width: 768px) {
  .upload-model-card {
    width: 200px;
  }
}